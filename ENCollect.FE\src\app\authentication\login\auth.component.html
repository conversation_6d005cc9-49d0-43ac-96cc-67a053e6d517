<section class="login-block">
   <div class="container-fluid bg-white">
      <div class="row">
         <div class="col-md-6 col-sm-12 p-0">
            <div class="row">
               <div class="col-md-3" [ngClass]="{'p-0': selectedTenantId != '3'}">
                  <img src="{{logo.logo}}" width="120%">
               </div>
               <div class="col-md-9 text-center p-0">
                  <h4 style="margin-top: 40px !important; font-size: 24px; font-weight: 700">Welcome To ENCollect™ Phoenix
                  </h4>
                  <p class="align-center" style="color: #000;"><b>Digital. Contactless.</b></p>
               </div>
               <div class="col-md-12 col-sm-12">
                  <form [formGroup]="authForm" class="login-form">
                     <!-- Form Fields -->
                     <fieldset [disabled]="isSubmitting" style="margin-left:40px;margin-right:40px;">
                        <div class="row form-group">
                           <div class="form-group">
                              <input type="radio" name="userType" formControlName="userType" value="ldapgrant"> Bank Staff
                              <input type="radio" name="userType" formControlName="userType" value="password"> Agency Staff
                           </div>
                        </div>
                        <div class="row form-group">
                           <label>Company Name </label>
                        </div>
                        <div class="row form-group">
                           <input  type="text" class="form-control form-control-lg" placeholder="Enter company name" name="tenant" value="BOB"  disabled >
                        </div>
                        <div class="row form-group">
                           <label>User Name</label>
                           <input formControlName="email" appNotAllowSpace placeholder="Enter user name / Email ID" class="form-control form-control-lg" type="text" />
                        </div>
                        <div class="row form-group">
                           <div class="w-100 d-flex justify-content-between">
                              <label>Password</label>
                              <a href="#" class="float-right" routerLink="/account/recovery">Forgot Password?</a>
                           </div>
                           <div style="position: relative; width: 100%;">
                              <input formControlName="password" appNotAllowSpace placeholder="Password"
                                 [type]="showPassword ? 'text' : 'password'" class="form-control form-control-lg" style="padding-right: 40px;" />
                              <i (click)="togglePasswordVisibility()"
                              [class]="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"
                              style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); cursor: pointer;"></i>
                           </div>
                        </div>
                        <div class="row form-group">
                           <div class="captcha-container">
                              <label>Captcha:<i class="mandate" style="color:red">*</i></label><br>
                              <input
                              type="text"
                              id="txtCaptcha"
                              class="captchabox"
                              [(ngModel)]="captcha"
                              name="captcha"
                              [ngModelOptions]="{standalone: true}"
                              #captchaCntrl="ngModel"
                              onDrag="return false"
                              onCopy="return false"
                              readonly
                              autocomplete="off"
                              />
                              <a href="javascript:void(0)" (click)="refreshCaptcha()">
                              <i class="fas fa-sync"></i>
                              </a>
                           </div>
                        </div>
                        <div class="row form-group">
                           <label>Enter Captcha:<i class="mandate" style="color:red">*</i></label><br>
                           <input
                           type="text"
                           class="form-control"
                           [(ngModel)]="capt"
                           name="captchainput"
                           onpaste="return false;"
                           onDrop="return false"
                           [ngModelOptions]="{standalone: true}"
                           #captchainputCntrl="ngModel"
                           placeholder="Please enter captcha"
                           [disabled]="!captcha"
                           required
                           />
                          
                        </div>
                        <div class="clearfix">
                           <button *ngIf="authForm.get('userType').value === 'password'" [disabled]="disableButton || serverBusy" id="submit" (click)="submitForm()" class="btn btn-primary float-right" [disabled]="!authForm.valid" type="submit">
                           Login
                           </button>&nbsp; &nbsp;&nbsp;
                           <button *ngIf="authForm.get('userType').value === 'ldapgrant'" [disabled]="disableButton || serverBusy" class="btn btn-primary float-right" [disabled]="!authForm.valid" type="submit" (click)="submitFormStaff()">
                           Login
                           </button>
                        </div>
                     </fieldset>
                     <div class="sign-info">
                        <span class="dark-color d-inline-block line-height-2 ">
                        Don't have an account? <a routerLink="/">Sign up</a><br />
                        </span>
                        <span style="text-align: center;">
                        <img src="assets/images/Poweredby.png" width="60%">
                        </span>
                     </div>
                  </form>
               </div>
            </div>
         </div>
         <div class="col-md-6 col-sm-12 p-0">
            <carousel [interval]="5000">
               <slide *ngFor="let item of imgArray" class="paddingClass" style="justify-content: flex-end; height: 100%;">
                  <img class="img-fluild" [src]="item.imgUrl" style="display: block; width: 100%;height: 100%;">
               </slide>
            </carousel>
         </div>
      </div>
   </div>
</section>
<style type="text/css">
   .carousel-control.left {
   background-image: none !important;
   }
   .carousel-control.right {
   background-image: none !important;
   }
   .f-30 {
   font-size: 30px;
   }
   .center {
   display: block;
   margin-left: auto;
   margin-right: auto;
   width: 50%;
   }
   .captchabox{
   background-image:url('assets/images/captcha.JPG');
   background-repeat:no-repeat;
   padding-left:20px;
   font-weight: bolder;
   font-size: 25px;
   text-align: center;
   }
   .captcha-container {
   display: flex;
   align-items: center;
   }
   .captcha-container input {
   margin-right: 10px;
   }
   .captcha-container a {
   display: inline-flex;
   align-items: center;
   }
</style>