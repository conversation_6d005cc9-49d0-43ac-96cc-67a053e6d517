{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./trails-history.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./trails-history.component.css?ngResource\";\nexport class SearchControls {\n  constructor() {\n    this.productGroup = \"\";\n    this.product = \"\";\n    this.subProduct = \"\";\n    this.bomBucket = \"\";\n    this.name = \"\";\n    this.region = \"\";\n    this.state = \"\";\n    this.city = \"\";\n    this.agency = \"\";\n    this.staffName = \"\";\n    this.branch = \"\";\n    this.saveFilterName = \"\";\n    this.filters = \"\";\n    this.agentName = \"\";\n    this.trailFromDate = \"\";\n    this.trailToDate = \"\";\n    this.trailGapDispositionCodeGroup = \"\";\n    this.trailGapDispositionCodes = \"\";\n    this.fieldAgencyOrTelecallinAgency = \"\";\n    this.fieldAgentOrTellecallingAgentOrStaff = \"\";\n  }\n}\nimport { Component, ChangeDetectorRef } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { mergeMap, map } from 'rxjs/operators';\nimport { ReportService } from '../reports.service';\nimport { ReportConfigService } from '../reportsconfig.service';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nlet TrailsHistoryComponent = class TrailsHistoryComponent {\n  constructor(reportService, reportConfigService, toastr, modalService, changeDetector) {\n    this.reportService = reportService;\n    this.reportConfigService = reportConfigService;\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.changeDetector = changeDetector;\n    this.searchControls = new SearchControls();\n    this.loader = {\n      isSearching: false,\n      productGroup: false,\n      product: false,\n      subproduct: false,\n      bucket: false,\n      isDownload: false\n    };\n    this.productGroupList = [];\n    this.bucketList = [];\n    this.geomasterList = [];\n    this.basebranches = [];\n    this.agencyUser = false;\n    this.agentList = [];\n    this.agencyList = [];\n    this.AgencyName = '';\n    this.reportType = 'bank';\n    this.AgencyType = '';\n    this.depositionCodes = [];\n    this.dispgroupcodeListfilter = [];\n    this.productList = [];\n    this.subProductList = [];\n    this.zoneList = [];\n    this.stateList = [];\n    this.regionList = [];\n    this.cityList = [];\n    this.branchList = [];\n    this.branchName = '';\n    this.staff = '';\n    this.agentName = '';\n    this.bankUserList = [];\n    this.userDetails = JSON.parse(window.localStorage['currentUser']);\n  }\n  ngOnInit() {\n    this.getProductGroups();\n    this.maxDate = new Date();\n    this.maxDate.setDate(this.maxDate.getDate());\n    this.getAgencyList();\n    this.getBuckets();\n    this.getCountryList();\n    this.dispositionCodeCodeGroups();\n    this.getBranches();\n  }\n  getProductGroups() {\n    this.loader.productGroup = true;\n    this.reportService.getProductGroupList().subscribe(response => {\n      this.productGroupList = response;\n      this.productGroupList.splice(0, 0, {\n        \"id\": \"null\",\n        \"name\": \"All\"\n      });\n      this.loader.productGroup = false;\n    }, err => {\n      this.toastr.error(err);\n      this.loader.productGroup = false;\n    });\n  }\n  getBuckets() {\n    this.loader.bucket = true;\n    this.reportService.getBucketList().subscribe(response => {\n      this.bucketList = response;\n      this.loader.bucket = false;\n    }, err => {\n      this.loader.bucket = false;\n      this.toastr.error(err);\n    });\n  }\n  getCountryList() {\n    this.reportService.getMasterCountry().subscribe(response => {\n      this.geomasterList = response;\n      this.zoneList = this.reportConfigService.removeDuplicates(this.geomasterList, \"name\");\n    }, error => {\n      this.toastr.error(error);\n    });\n  }\n  getBranches() {\n    this.reportService.getBaseBranches().subscribe(basebranches => this.basebranches = basebranches, err => {\n      this.toastr.error(err);\n    });\n  }\n  getAgencyList() {\n    this.reportService.getFieldTeleAgencyName().subscribe(Response => {\n      this.agencyList = Response;\n      if (this.userDetails.responsibilityInfos[0].role == 'AgencyToBackEndExternalBIAP' || this.userDetails.responsibilityInfos[0].role == 'AgencyToFrontEndExternalBIAP' || this.userDetails.responsibilityInfos[0].role == 'AgencyToFrontEndExternalFOS' || this.userDetails.responsibilityInfos[0].role == 'SYSTEMADMIN' || this.userDetails.responsibilityInfos[0].role == 'AgencyToFrontEndExternalTC') {\n        this.agencyUser = true;\n        this.reportType = \"agency\";\n        let abc = this.userDetails.agencyFirstName + \" \" + this.userDetails.agencyLastName + \" \" + this.userDetails.agencyCode;\n        // this.AgencyName = abc.replace('null', \"\");\n        this.searchControls.agency = this.userDetails.agencyCode;\n        this.AgencyType = this.agencyList.find(v => v.agencyCode == this.userDetails.agencyCode).agencyType;\n        this.getAgentList(this.userDetails.agencyId);\n      } else {\n        this.agencyUser = false;\n      }\n    });\n  }\n  getAgentList(data) {\n    this.reportService.getFieldTeleAgents(data).subscribe(Response => {\n      this.agentList = Response;\n    });\n  }\n  dispositionCodeCodeGroups() {\n    this.dispgroupcodeListfilter = [];\n    this.depositionCodes = [];\n    this.reportService.getAllDespositionGroups().subscribe(resp => {\n      if (resp != null && resp.length > 0) {\n        this.dispgroupcodeListfilter = resp;\n      }\n    }, err => {\n      this.toastr.error(err);\n    });\n  }\n  dispCodeGrpChange(val) {\n    console.log(val);\n    this.depositionCodes = [];\n    for (let i = 0; i < val.length; i++) {\n      let dispgroup = this.dispgroupcodeListfilter.find(x => x.name == val[i]);\n      let data = {\n        \"dispositionGroupId\": dispgroup[\"id\"]\n      };\n      this.reportService.dispostionCode(data).subscribe(resp => {\n        this.depositionCodes.push(resp);\n        this.depositionCodes = this.depositionCodes.flat();\n      });\n    }\n  }\n  getProducts() {\n    this.productList = [];\n    this.subProductList = [];\n    this.searchControls.product = \"\";\n    this.searchControls.subProduct = \"\";\n    if (this.searchControls.productGroup != \"All\" && this.searchControls.productGroup != \"\") {\n      this.loader.product = true;\n      this.reportService.getProductListByPG(this.searchControls.productGroup).subscribe(response => {\n        this.productList = response;\n        this.productList.splice(0, 0, {\n          \"id\": \"null\",\n          \"name\": \"All\"\n        });\n        this.loader.product = false;\n      }, err => {\n        this.toastr.error(err);\n        this.loader.product = false;\n      });\n    } else if (this.searchControls.productGroup == \"All\") {\n      this.productList.push({\n        \"id\": \"null\",\n        \"name\": \"All\"\n      });\n    }\n  }\n  getSubProducts() {\n    this.subProductList = [];\n    this.searchControls.subProduct = \"\";\n    if (this.searchControls.product != \"null\") {\n      this.loader.subproduct = true;\n      this.reportService.getSubproductListByProduct(this.searchControls.product).subscribe(response => {\n        this.subProductList = response;\n        this.subProductList.splice(0, 0, {\n          \"id\": \"null\",\n          \"name\": \"All\"\n        });\n        this.loader.subproduct = false;\n      }, err => {\n        this.toastr.error(err);\n        this.loader.subproduct = false;\n      });\n    } else if (this.searchControls.product == \"null\") {\n      this.subProductList.push({\n        \"id\": \"null\",\n        \"name\": \"All\"\n      });\n    }\n  }\n  regionByZone() {\n    let data = {\n      \"country\": this.searchControls.name\n    };\n    this.searchControls.region = '';\n    this.searchControls.state = '';\n    this.searchControls.city = '';\n    this.searchControls.branch = '';\n    this.reportService.regionReport(data).subscribe(response => {\n      this.regionList = [];\n      this.regionList = response;\n    }, error => {\n      this.toastr.error(error);\n    });\n  }\n  stateByZone() {\n    let data = {\n      \"country\": this.searchControls.name,\n      \"region\": this.searchControls.region\n    };\n    this.searchControls.state = '';\n    this.searchControls.city = '';\n    this.searchControls.branch = '';\n    this.reportService.stateReport(data).subscribe(response => {\n      this.stateList = [];\n      this.stateList = response;\n    }, error => {\n      this.toastr.error(error);\n    });\n  }\n  citybyState() {\n    let data = {\n      \"country\": this.searchControls.name,\n      \"region\": this.searchControls.region,\n      \"state\": this.searchControls.state\n    };\n    this.searchControls.city = '';\n    this.searchControls.branch = '';\n    this.reportService.cityReport(data).subscribe(response => {\n      this.cityList = [];\n      this.cityList = response;\n    }, error => {\n      this.toastr.error(error);\n    });\n  }\n  branchByCity() {\n    let data = {\n      \"Country\": this.searchControls.name,\n      \"Region\": this.searchControls.region,\n      \"State\": this.searchControls.state,\n      \"City\": this.searchControls.city\n    };\n    this.searchControls.branch = '';\n    this.reportService.branchReport(data).subscribe(response => {\n      this.branchList = [];\n      this.branchList = response;\n    }, error => {\n      this.toastr.error(error);\n    });\n  }\n  resetvaluesNew() {\n    if (this.reportType == \"bank\") {\n      this.searchControls.agency = \"\";\n      this.searchControls.staffName = \"\";\n      this.searchControls.branch = \"\";\n      this.branchName = \"\";\n      this.AgencyName = \"\";\n      this.staff = \"\";\n      this.agentName = \"\";\n      this.searchControls.fieldAgentOrTellecallingAgentOrStaff = \"\";\n      this.searchControls.fieldAgencyOrTelecallinAgency = \"\";\n    } else {\n      this.searchControls.staffName = \"\";\n      this.searchControls.branch = \"\";\n      this.searchControls.agency = \"\";\n      this.branchName = \"\";\n      this.AgencyName = \"\";\n      this.staff = \"\";\n      this.agentName = \"\";\n      this.searchControls.fieldAgentOrTellecallingAgentOrStaff = \"\";\n      this.searchControls.fieldAgencyOrTelecallinAgency = \"\";\n    }\n  }\n  staffNameEmpty() {\n    this.searchControls.staffName = '';\n  }\n  agentNameEmpty() {\n    this.searchControls.agentName = '';\n  }\n  onAgencySelect(event) {\n    this.AgencyName = event.item.firstName + \"-\" + event.item.agencyCode;\n    this.searchControls.agency = event.item.agencyCode;\n    this.searchControls.branch = '';\n    this.AgencyType = event.item.agencyType;\n    this.agentName = '';\n    this.searchControls.agentName = '';\n    this.searchControls.fieldAgencyOrTelecallinAgency = event.item.id;\n    this.getAgentList(event.item.id);\n  }\n  onAgentSelect(event) {\n    this.agentName = event.item.name + \"-\" + event.item.code;\n    this.searchControls.agentName = event.item.code;\n    this.searchControls.fieldAgentOrTellecallingAgentOrStaff = event.item.id;\n  }\n  onBranchSelect(event) {\n    this.branchName = event.item.name + \"-\" + event.item.code;\n    this.searchControls.branch = event.item.name;\n    this.searchControls.agency = '';\n  }\n  onSelectStaff(event) {\n    this.staff = event.item.firstName + \" \" + event.item.agencyCode;\n    this.searchControls.staffName = event.item.agencyCode; //  agencyCode in json is actually staff code as per BE discussion\n    this.searchControls.fieldAgentOrTellecallingAgentOrStaff = event.item.id;\n  }\n  branchNoResults(event) {\n    this.branchnoResult = event;\n  }\n  agencyNoResults(event) {\n    this.agencynoResult = event;\n  }\n  agentNoResults(event) {\n    this.agentnoResult = event;\n  }\n  typeaheadNoResults(event) {\n    if (event) {\n      this.toastr.info(\"Please enter correct supervising manager\");\n    }\n  }\n  agentChangeLoading(event) {\n    this.agenttypeaheadLoading = event;\n  }\n  agencyChangeLoading(event) {\n    this.agencytypeaheadLoading = event;\n  }\n  branchChangeLoading(event) {\n    this.branchtypeaheadLoading = event;\n  }\n  getSupervisorList() {\n    this.bankUserList = Observable.create(observer => {\n      observer.next(this.staff);\n    }).pipe(mergeMap(token => this.getsearchSupervisor(token)));\n  }\n  getsearchSupervisor(token) {\n    return this.reportService.getSupervisor(token).pipe(map(results => results.filter(res => res.firstName.toLowerCase().indexOf(token.toLowerCase()) > -1)));\n  }\n  genReport() {\n    this.loader.isSearching = true;\n    let obj = Object.assign({}, this.searchControls);\n    obj[\"productGroup\"] = this.searchControls.productGroup ? this.reportConfigService.findObjectByKey(this.productGroupList, \"id\", this.searchControls.productGroup)[\"name\"] : null;\n    obj[\"product\"] = this.searchControls.product ? this.reportConfigService.findObjectByKey(this.productList, \"id\", this.searchControls.product)[\"name\"] : null;\n    // Convert dates to proper format to avoid timezone issues\n    if (typeof this.searchControls.trailFromDate == 'object' && this.searchControls.trailFromDate != null) {\n      obj[\"trailFromDate\"] = this.reportConfigService.convertDate(this.searchControls.trailFromDate);\n    }\n    if (typeof this.searchControls.trailToDate == 'object' && this.searchControls.trailToDate != null) {\n      obj[\"trailToDate\"] = this.reportConfigService.convertDate(this.searchControls.trailToDate);\n    }\n    if (this.reportType == \"bank\") {\n      obj[\"isCompanyuser\"] = true;\n    } else {\n      obj[\"isCompanyuser\"] = false;\n    }\n    this.searchControls[\"branch\"] = this.searchControls.branch ? this.searchControls.branch : null;\n    this.reportService.downloadtrailHistory(obj).subscribe(response => {\n      this.loader.isSearching = false;\n      this.downloadFile(response);\n    }, err => {\n      this.toastr.error(err);\n      this.loader.isSearching = false;\n    });\n  }\n  downloadFile(filename) {\n    this.reportService.downloadFile(filename).subscribe(response => {\n      this.loader.isDownload = false;\n      var imagetype = \"zip\";\n      var mediaType = 'application/zip';\n      var a = document.createElement(\"a\");\n      a.setAttribute('style', 'display:none;');\n      document.body.appendChild(a);\n      var blob = new Blob([response], {\n        type: mediaType\n      });\n      var url = window.URL.createObjectURL(blob);\n      a.href = url;\n      a.download = 'TrailHistoryReport.zip';\n      a.click();\n    }, err => {\n      this.toastr.error(err);\n      this.loader.isDownload = false;\n    });\n  }\n  static #_ = this.ctorParameters = () => [{\n    type: ReportService\n  }, {\n    type: ReportConfigService\n  }, {\n    type: ToastrService\n  }, {\n    type: BsModalService\n  }, {\n    type: ChangeDetectorRef\n  }];\n};\nTrailsHistoryComponent = __decorate([Component({\n  selector: 'app-trails-history',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], TrailsHistoryComponent);\nexport { TrailsHistoryComponent };", "map": {"version": 3, "names": ["SearchControls", "constructor", "productGroup", "product", "subProduct", "bomBucket", "name", "region", "state", "city", "agency", "staffName", "branch", "saveFilterName", "filters", "<PERSON><PERSON><PERSON>", "trailFromDate", "trailToDate", "trailGapDispositionCodeGroup", "trailGapDispositionCodes", "fieldAgencyOrTelecallinAgency", "fieldAgentOrTellecallingAgentOrStaff", "Component", "ChangeDetectorRef", "Observable", "mergeMap", "map", "ReportService", "ReportConfigService", "ToastrService", "BsModalService", "TrailsHistoryComponent", "reportService", "reportConfigService", "toastr", "modalService", "changeDetector", "searchControls", "loader", "isSearching", "subproduct", "bucket", "isDownload", "productGroupList", "bucketList", "geomasterList", "basebranches", "agencyUser", "agentList", "agencyList", "AgencyName", "reportType", "AgencyType", "depositionCodes", "dispgroupcodeListfilter", "productList", "subProductList", "zoneList", "stateList", "regionList", "cityList", "branchList", "branchName", "staff", "bankUserList", "userDetails", "JSON", "parse", "window", "localStorage", "ngOnInit", "getProductGroups", "maxDate", "Date", "setDate", "getDate", "getAgencyList", "getBuckets", "getCountryList", "dispositionCodeCodeGroups", "getBranches", "getProductGroupList", "subscribe", "response", "splice", "err", "error", "getBucketList", "getMasterCountry", "removeDuplicates", "getBaseBranches", "getFieldTeleAgencyName", "Response", "responsibilityInfos", "role", "abc", "agencyFirstName", "agencyLastName", "agencyCode", "find", "v", "agencyType", "getAgentList", "agencyId", "data", "getFieldTeleAgents", "getAllDespositionGroups", "resp", "length", "dispCodeGrpChange", "val", "console", "log", "i", "dispgroup", "x", "dispostionCode", "push", "flat", "getProducts", "getProductListByPG", "getSubProducts", "getSubproductListByProduct", "regionByZone", "regionReport", "stateByZone", "stateReport", "citybyState", "cityReport", "branchByCity", "branchReport", "resetvalues<PERSON>ew", "staffNameEmpty", "agentNameEmpty", "onAgencySelect", "event", "item", "firstName", "id", "onAgentSelect", "code", "onBranchSelect", "onSelectStaff", "branchNoResults", "branchnoResult", "agencyNoResults", "agencynoResult", "agentNoResults", "agentnoResult", "typeaheadNoResults", "info", "agentChangeLoading", "agenttypeaheadLoading", "agencyChangeLoading", "agencytypeaheadLoading", "branchChangeLoading", "branchtypeaheadLoading", "getSupervisorList", "create", "observer", "next", "pipe", "token", "getsearchSupervisor", "getSupervisor", "results", "filter", "res", "toLowerCase", "indexOf", "genReport", "obj", "Object", "assign", "findObjectByKey", "convertDate", "downloadtrailHistory", "downloadFile", "filename", "imagetype", "mediaType", "a", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "blob", "Blob", "type", "url", "URL", "createObjectURL", "href", "download", "click", "_", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\BOBQAACM\\ENCollect.FE\\src\\app\\reports\\trails-history\\trails-history.component.ts"], "sourcesContent": ["export class SearchControls{\r\n  productGroup: string= \"\";\r\n  product: string= \"\";\r\n  subProduct: string= \"\";\r\n  bomBucket: string= \"\";\r\n  name: string= \"\";\r\n  region: string= \"\";\r\n  state: string= \"\";\r\n  city: string= \"\";\r\n  agency: string= \"\";\r\n  staffName: string= \"\";\r\n  branch: string= \"\";\r\n  saveFilterName: string= \"\";\r\n  filters:string= \"\";\r\n  agentName: string= \"\";\r\n  trailFromDate: any=\"\";\r\n  trailToDate: any=\"\";\r\n  trailGapDispositionCodeGroup: any = \"\";\r\n  trailGapDispositionCodes : any = \"\";\r\n  fieldAgencyOrTelecallinAgency : string = \"\";\r\n  fieldAgentOrTellecallingAgentOrStaff: string = \"\";\r\n}\r\nimport { Component, OnInit,ElementRef,ViewChild, TemplateRef,ChangeDetectorRef   } from '@angular/core';\r\nimport { Observable, of } from 'rxjs';\r\nimport { mergeMap,map } from 'rxjs/operators';\r\n\r\nimport { ReportService } from '../reports.service';\r\nimport { ReportConfigService } from '../reportsconfig.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';\r\n\r\n@Component({\r\n  selector: 'app-trails-history',\r\n  templateUrl: './trails-history.component.html',\r\n  styleUrls: ['./trails-history.component.css']\r\n})\r\nexport class TrailsHistoryComponent implements OnInit {\r\nsearchControls: SearchControls = new SearchControls();\r\nloader = {\r\n     isSearching: false,\r\n     productGroup: false,\r\n     product: false,\r\n     subproduct: false,\r\n     bucket: false,\r\n     isDownload: false\r\n}\r\nproductGroupList: any = [];\r\nbucketList: any = [];\r\ngeomasterList: any = [];\r\nmaxDate: Date;\r\nbasebranches: any = [];\r\nuserDetails: any;\r\nagencyUser: boolean = false;\r\nagentList: any=[];\r\nagencyList: any = [];\r\nAgencyName: string= '';\r\nreportType: string = 'bank';\r\nAgencyType: string = '';\r\ndepositionCodes: any = []\r\ndispgroupcodeListfilter = [];\r\nproductList: any = [];\r\nsubProductList: any = [];\r\nzoneList: any = [];\r\nstateList: any = [];\r\nregionList: any = [];\r\ncityList: any = [];\r\nbranchList: any = [];\r\n\r\nbranchName: string = '';\r\nstaff: string = '';\r\nagentName: string = '';\r\n\r\nconstructor(private reportService: ReportService, private reportConfigService: ReportConfigService,public toastr: ToastrService,\r\n  private modalService: BsModalService,private changeDetector : ChangeDetectorRef) {\r\n  this.userDetails = JSON.parse(window.localStorage['currentUser']);\r\n}\r\n\r\nngOnInit(){\r\n   this.getProductGroups();\r\n   this.maxDate =new Date();\r\n   this.maxDate.setDate(this.maxDate.getDate());\r\n   this.getAgencyList()\r\n   this.getBuckets();\r\n   this.getCountryList();\r\n   this.dispositionCodeCodeGroups();\r\n   this.getBranches()\r\n}\r\n\r\ngetProductGroups(){\r\n   this.loader.productGroup = true;\r\n    this.reportService.getProductGroupList().subscribe(response=> {\r\n         this.productGroupList = response\r\n         this.productGroupList.splice( 0, 0, {\"id\":\"null\",\"name\":\"All\"});\r\n         this.loader.productGroup = false;\r\n     },err=>{\r\n          this.toastr.error(err)\r\n          this.loader.productGroup = false;\r\n     });\r\n}\r\n\r\ngetBuckets(){\r\n    this.loader.bucket = true;\r\n    this.reportService.getBucketList().subscribe(response => {\r\n        this.bucketList = response\r\n        this.loader.bucket = false;\r\n    },err=>{\r\n        this.loader.bucket = false;\r\n           this.toastr.error(err)\r\n    });\r\n}\r\n\r\ngetCountryList(){\r\n    this.reportService.getMasterCountry().subscribe(response =>{\r\n      this.geomasterList = response;\r\n     this.zoneList= this.reportConfigService.removeDuplicates(this.geomasterList,\"name\")\r\n    },(error) => {\r\n         this.toastr.error(error)\r\n    });\r\n}\r\n\r\ngetBranches(){\r\n  this.reportService.getBaseBranches().subscribe(basebranches=> this.basebranches = basebranches,err=>{ this.toastr.error(err) });\r\n}\r\n\r\ngetAgencyList() {\r\n    this.reportService.getFieldTeleAgencyName().subscribe(Response => { // get collectors list for auto predictive\r\n      this.agencyList = Response;\r\n      if (this.userDetails.responsibilityInfos[0].role == 'AgencyToBackEndExternalBIAP' ||\r\n        this.userDetails.responsibilityInfos[0].role == 'AgencyToFrontEndExternalBIAP' ||\r\n        this.userDetails.responsibilityInfos[0].role == 'AgencyToFrontEndExternalFOS' ||\r\n        this.userDetails.responsibilityInfos[0].role == 'SYSTEMADMIN' ||\r\n        this.userDetails.responsibilityInfos[0].role == 'AgencyToFrontEndExternalTC') {\r\n        this.agencyUser = true;\r\n        this.reportType = \"agency\"\r\n        let abc = this.userDetails.agencyFirstName + \" \" + this.userDetails.agencyLastName + \" \" + this.userDetails.agencyCode\r\n        // this.AgencyName = abc.replace('null', \"\");\r\n        this.searchControls.agency = this.userDetails.agencyCode;\r\n        this.AgencyType = this.agencyList.find(v=>v.agencyCode == this.userDetails.agencyCode).agencyType;\r\n        this.getAgentList(this.userDetails.agencyId)\r\n      } else {\r\n        this.agencyUser = false;\r\n      }\r\n    });\r\n}\r\n\r\n\r\ngetAgentList(data){\r\n    this.reportService.getFieldTeleAgents(data).subscribe(Response => { // get collectors list for auto predictive\r\n       this.agentList = Response;\r\n   });\r\n}\r\n\r\ndispositionCodeCodeGroups() {\r\n    this.dispgroupcodeListfilter = []\r\n    this.depositionCodes = []\r\n    this.reportService.getAllDespositionGroups().subscribe(resp => {\r\n      if (resp != null && resp.length > 0) {\r\n        this.dispgroupcodeListfilter = resp\r\n      }\r\n    }, err => {\r\n      this.toastr.error(err)\r\n    })\r\n }\r\n\r\n dispCodeGrpChange(val) {\r\n   console.log(val)\r\n    this.depositionCodes = [];\r\n    for(let i=0; i<val.length;i++){\r\n       let dispgroup = this.dispgroupcodeListfilter.find(x => x.name == val[i])\r\n       let data = {\r\n        \"dispositionGroupId\": dispgroup[\"id\"]\r\n       }\r\n      this.reportService.dispostionCode(data).subscribe(resp => {\r\n        this.depositionCodes.push(resp);\r\n        this.depositionCodes = this.depositionCodes.flat();\r\n      })\r\n    }\r\n  }\r\n\r\n\r\n\r\n getProducts(){\r\n   this.productList = []\r\n   this.subProductList = []\r\n   this.searchControls.product = \"\"\r\n   this.searchControls.subProduct = \"\"\r\n   if(this.searchControls.productGroup!=\"All\" && this.searchControls.productGroup!=\"\"){\r\n     this.loader.product = true\r\n    this.reportService.getProductListByPG(this.searchControls.productGroup).subscribe(response=> {\r\n        this.productList = response\r\n        this.productList.splice( 0, 0, {\"id\":\"null\",\"name\":\"All\"});\r\n        this.loader.product = false\r\n      },\r\n       err=>{\r\n          this.toastr.error(err)\r\n          this.loader.product = false\r\n     });\r\n    } else if(this.searchControls.productGroup==\"All\"){\r\n      this.productList.push({\"id\":\"null\",\"name\":\"All\"});\r\n    }\r\n }\r\n\r\n getSubProducts(){\r\n    this.subProductList = []\r\n    this.searchControls.subProduct = \"\"\r\n    if(this.searchControls.product!=\"null\"){\r\n       this.loader.subproduct = true\r\n       this.reportService.getSubproductListByProduct(this.searchControls.product).subscribe(response=> {\r\n          this.subProductList = response\r\n          this.subProductList.splice( 0, 0, {\"id\":\"null\",\"name\":\"All\"});\r\n           this.loader.subproduct = false;\r\n       },err=>{\r\n            this.toastr.error(err)\r\n             this.loader.subproduct = false;\r\n       });\r\n    }else if(this.searchControls.product==\"null\"){\r\n      this.subProductList.push({\"id\":\"null\",\"name\":\"All\"});\r\n    }\r\n }\r\n\r\n regionByZone(){\r\n    let data={\r\n      \"country\":this.searchControls.name,\r\n    }\r\n    this.searchControls.region = ''\r\n    this.searchControls.state = ''\r\n    this.searchControls.city = ''\r\n    this.searchControls.branch = ''\r\n    this.reportService.regionReport(data).subscribe((response: any) => {\r\n      this.regionList=[];\r\n      this.regionList=response;\r\n    },error=>{\r\n       this.toastr.error(error)\r\n    })\r\n}\r\n\r\n stateByZone(){\r\n  let data={\r\n    \"country\": this.searchControls.name,\r\n    \"region\":this.searchControls.region,\r\n  }\r\n  this.searchControls.state = ''\r\n  this.searchControls.city = ''\r\n  this.searchControls.branch = ''\r\n  this.reportService.stateReport(data).subscribe((response: any) => {\r\n    this.stateList=[];\r\n    this.stateList=response;\r\n\r\n   },error=>{\r\n     this.toastr.error(error)\r\n   })\r\n}\r\n\r\ncitybyState(){\r\n  let data={\r\n    \"country\": this.searchControls.name,\r\n    \"region\": this.searchControls.region,\r\n    \"state\":this.searchControls.state,\r\n  }\r\n  this.searchControls.city = ''\r\n  this.searchControls.branch = ''\r\n  this.reportService.cityReport(data).subscribe((response: any) => {\r\n    this.cityList=[];\r\n    this.cityList=response;\r\n  },error=>{\r\n     this.toastr.error(error)\r\n  })\r\n}\r\n\r\nbranchByCity(){\r\n let data={\r\n   \"Country\": this.searchControls.name,\r\n   \"Region\": this.searchControls.region,\r\n   \"State\": this.searchControls.state,\r\n    \"City\":this.searchControls.city,\r\n }\r\n  this.searchControls.branch = ''\r\n  this.reportService.branchReport(data).subscribe((response: any) => {\r\n  this.branchList=[];\r\n  this.branchList=response;\r\n  },error=>{\r\n   this.toastr.error(error)\r\n  })\r\n}\r\n\r\nresetvaluesNew(){\r\n      if(this.reportType==\"bank\"){\r\n        this.searchControls.agency = \"\"\r\n        this.searchControls.staffName = \"\"\r\n        this.searchControls.branch = \"\"\r\n        this.branchName = \"\"\r\n        this.AgencyName = \"\"\r\n        this.staff = \"\"\r\n        this.agentName = \"\"\r\n        this.searchControls.fieldAgentOrTellecallingAgentOrStaff = \"\"\r\n        this.searchControls.fieldAgencyOrTelecallinAgency = \"\"\r\n      } else {\r\n        this.searchControls.staffName = \"\"\r\n        this.searchControls.branch = \"\"\r\n        this.searchControls.agency = \"\"\r\n        this.branchName = \"\"\r\n        this.AgencyName = \"\"\r\n        this.staff = \"\"\r\n        this.agentName = \"\"\r\n        this.searchControls.fieldAgentOrTellecallingAgentOrStaff = \"\"\r\n        this.searchControls.fieldAgencyOrTelecallinAgency = \"\"\r\n      }\r\n  }\r\n  staffNameEmpty(){\r\n    this.searchControls.staffName = ''\r\n  }\r\n  agentNameEmpty(){\r\n    this.searchControls.agentName = ''\r\n  }\r\n\r\n  onAgencySelect(event){\r\n      this.AgencyName =  event.item.firstName+\"-\"+event.item.agencyCode\r\n      this.searchControls.agency = event.item.agencyCode;\r\n      this.searchControls.branch = '';\r\n      this.AgencyType =  event.item.agencyType\r\n      this.agentName = ''\r\n      this.searchControls.agentName = ''\r\n      this.searchControls.fieldAgencyOrTelecallinAgency = event.item.id\r\n      this.getAgentList(event.item.id)\r\n  }\r\n\r\n  onAgentSelect(event){\r\n    this.agentName =  event.item.name+\"-\"+event.item.code\r\n    this.searchControls.agentName = event.item.code\r\n    this.searchControls.fieldAgentOrTellecallingAgentOrStaff = event.item.id;\r\n  }\r\n\r\n  onBranchSelect(event){\r\n     this.branchName =  event.item.name+ \"-\" + event.item.code\r\n     this.searchControls.branch = event.item.name;\r\n     this.searchControls.agency = '';\r\n  }\r\n\r\n  onSelectStaff(event){\r\n    this.staff =  event.item.firstName+\" \"+event.item.agencyCode\r\n    this.searchControls.staffName = event.item.agencyCode //  agencyCode in json is actually staff code as per BE discussion\r\n    this.searchControls.fieldAgentOrTellecallingAgentOrStaff = event.item.id\r\n  }\r\n\r\n   branchnoResult: any;\r\n   agencynoResult: any;\r\n   agentnoResult: any;\r\n   agenttypeaheadLoading: any;\r\n   agencytypeaheadLoading: any;\r\n   branchtypeaheadLoading: any;\r\n   bankUserList: any = [];\r\n   branchNoResults(event: boolean): void {\r\n      this.branchnoResult = event;\r\n  }\r\n\r\n   agencyNoResults(event: boolean): void {\r\n      this.agencynoResult = event;\r\n  }\r\n\r\n    agentNoResults(event: boolean): void {\r\n      this.agentnoResult = event;\r\n  }\r\n\r\n   typeaheadNoResults(event: boolean): void {\r\n    if(event){\r\n      this.toastr.info(\"Please enter correct supervising manager\");\r\n    }\r\n  }\r\n\r\n  agentChangeLoading(event: boolean): void {\r\n    this.agenttypeaheadLoading = event;\r\n  }\r\n\r\n    agencyChangeLoading(event: boolean): void {\r\n    this.agencytypeaheadLoading = event;\r\n  }\r\n\r\n   branchChangeLoading(event: boolean): void {\r\n    this.branchtypeaheadLoading = event;\r\n  }\r\n\r\n\r\n  getSupervisorList(){\r\n     this.bankUserList = Observable.create((observer: any) => {\r\n            observer.next(this.staff);\r\n        })\r\n        .pipe(\r\n          mergeMap((token: string) => this.getsearchSupervisor(token))\r\n        );\r\n  }\r\n\r\n  getsearchSupervisor(token: string):  Observable<any> {\r\n    return this.reportService.getSupervisor(token)\r\n    .pipe(map((results: any[]) => results.filter(res => res.firstName.toLowerCase().indexOf(token.toLowerCase()) > -1)));\r\n  }\r\n\r\n  genReport(){\r\n    this.loader.isSearching = true;\r\n    let obj = Object.assign({}, this.searchControls)\r\n    obj[\"productGroup\"] = this.searchControls.productGroup ? this.reportConfigService.findObjectByKey(this.productGroupList,\"id\",this.searchControls.productGroup)[\"name\"] : null\r\n     obj[\"product\"] = this.searchControls.product  ? this.reportConfigService.findObjectByKey(this.productList,\"id\",this.searchControls.product)[\"name\"] : null\r\n\r\n     // Convert dates to proper format to avoid timezone issues\r\n     if((typeof(this.searchControls.trailFromDate) == 'object') && this.searchControls.trailFromDate != null){\r\n       obj[\"trailFromDate\"] = this.reportConfigService.convertDate(this.searchControls.trailFromDate);\r\n     }\r\n     if((typeof(this.searchControls.trailToDate) == 'object') && this.searchControls.trailToDate != null){\r\n       obj[\"trailToDate\"] = this.reportConfigService.convertDate(this.searchControls.trailToDate);\r\n     }\r\n\r\n     if (this.reportType == \"bank\") {\r\n      obj[\"isCompanyuser\"] = true;\r\n     } else {\r\n      obj[\"isCompanyuser\"]  = false;\r\n     }\r\n     this.searchControls[\"branch\"] = this.searchControls.branch ? this.searchControls.branch : null;\r\n\r\n    this.reportService.downloadtrailHistory(obj).subscribe(response=> {\r\n      this.loader.isSearching = false\r\n      this.downloadFile(response)\r\n    },err=>{\r\n      this.toastr.error(err);\r\n      this.loader.isSearching = false\r\n    })\r\n  }\r\n\r\n  downloadFile(filename){\r\n    this.reportService.downloadFile(filename).subscribe(response=> {\r\n          this.loader.isDownload = false\r\n          var imagetype = \"zip\";\r\n          var mediaType = 'application/zip';\r\n          var a = document.createElement(\"a\");\r\n          a.setAttribute('style', 'display:none;');\r\n          document.body.appendChild(a);\r\n          var blob = new Blob([response], { type: mediaType });\r\n          var url= window.URL.createObjectURL(blob);\r\n          a.href = url;\r\n          a.download = 'TrailHistoryReport.zip';\r\n          a.click();\r\n    },err=>{\r\n        this.toastr.error(err);\r\n        this.loader.isDownload = false\r\n    })\r\n  }\r\n\r\n\r\n\r\n}\r\n"], "mappings": ";;;AAAA,OAAM,MAAOA,cAAc;EAA3BC,YAAA;IACE,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,IAAI,GAAU,EAAE;IAChB,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,KAAK,GAAU,EAAE;IACjB,KAAAC,IAAI,GAAU,EAAE;IAChB,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,OAAO,GAAS,EAAE;IAClB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,aAAa,GAAM,EAAE;IACrB,KAAAC,WAAW,GAAM,EAAE;IACnB,KAAAC,4BAA4B,GAAQ,EAAE;IACtC,KAAAC,wBAAwB,GAAS,EAAE;IACnC,KAAAC,6BAA6B,GAAY,EAAE;IAC3C,KAAAC,oCAAoC,GAAW,EAAE;EACnD;;AACA,SAASC,SAAS,EAA2CC,iBAAiB,QAAU,eAAe;AACvG,SAASC,UAAU,QAAY,MAAM;AACrC,SAASC,QAAQ,EAACC,GAAG,QAAQ,gBAAgB;AAE7C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAAqBC,cAAc,QAAQ,qBAAqB;AAOzD,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAoCnC9B,YAAoB+B,aAA4B,EAAUC,mBAAwC,EAAQC,MAAqB,EACrHC,YAA4B,EAASC,cAAkC;IAD7D,KAAAJ,aAAa,GAAbA,aAAa;IAAyB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAA6B,KAAAC,MAAM,GAANA,MAAM;IACtG,KAAAC,YAAY,GAAZA,YAAY;IAAyB,KAAAC,cAAc,GAAdA,cAAc;IApC7D,KAAAC,cAAc,GAAmB,IAAIrC,cAAc,EAAE;IACrD,KAAAsC,MAAM,GAAG;MACJC,WAAW,EAAE,KAAK;MAClBrC,YAAY,EAAE,KAAK;MACnBC,OAAO,EAAE,KAAK;MACdqC,UAAU,EAAE,KAAK;MACjBC,MAAM,EAAE,KAAK;MACbC,UAAU,EAAE;KAChB;IACD,KAAAC,gBAAgB,GAAQ,EAAE;IAC1B,KAAAC,UAAU,GAAQ,EAAE;IACpB,KAAAC,aAAa,GAAQ,EAAE;IAEvB,KAAAC,YAAY,GAAQ,EAAE;IAEtB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,SAAS,GAAM,EAAE;IACjB,KAAAC,UAAU,GAAQ,EAAE;IACpB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,UAAU,GAAW,MAAM;IAC3B,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAQ,EAAE;IACzB,KAAAC,uBAAuB,GAAG,EAAE;IAC5B,KAAAC,WAAW,GAAQ,EAAE;IACrB,KAAAC,cAAc,GAAQ,EAAE;IACxB,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,UAAU,GAAQ,EAAE;IACpB,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,UAAU,GAAQ,EAAE;IAEpB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAhD,SAAS,GAAW,EAAE;IAwRnB,KAAAiD,YAAY,GAAQ,EAAE;IApRvB,IAAI,CAACC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,aAAa,CAAC,CAAC;EACnE;EAEAC,QAAQA,CAAA;IACL,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,OAAO,GAAE,IAAIC,IAAI,EAAE;IACxB,IAAI,CAACD,OAAO,CAACE,OAAO,CAAC,IAAI,CAACF,OAAO,CAACG,OAAO,EAAE,CAAC;IAC5C,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,WAAW,EAAE;EACrB;EAEAT,gBAAgBA,CAAA;IACb,IAAI,CAACjC,MAAM,CAACpC,YAAY,GAAG,IAAI;IAC9B,IAAI,CAAC8B,aAAa,CAACiD,mBAAmB,EAAE,CAACC,SAAS,CAACC,QAAQ,IAAE;MACxD,IAAI,CAACxC,gBAAgB,GAAGwC,QAAQ;MAChC,IAAI,CAACxC,gBAAgB,CAACyC,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE;QAAC,IAAI,EAAC,MAAM;QAAC,MAAM,EAAC;MAAK,CAAC,CAAC;MAC/D,IAAI,CAAC9C,MAAM,CAACpC,YAAY,GAAG,KAAK;IACpC,CAAC,EAACmF,GAAG,IAAE;MACF,IAAI,CAACnD,MAAM,CAACoD,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAAC/C,MAAM,CAACpC,YAAY,GAAG,KAAK;IACrC,CAAC,CAAC;EACP;EAEA2E,UAAUA,CAAA;IACN,IAAI,CAACvC,MAAM,CAACG,MAAM,GAAG,IAAI;IACzB,IAAI,CAACT,aAAa,CAACuD,aAAa,EAAE,CAACL,SAAS,CAACC,QAAQ,IAAG;MACpD,IAAI,CAACvC,UAAU,GAAGuC,QAAQ;MAC1B,IAAI,CAAC7C,MAAM,CAACG,MAAM,GAAG,KAAK;IAC9B,CAAC,EAAC4C,GAAG,IAAE;MACH,IAAI,CAAC/C,MAAM,CAACG,MAAM,GAAG,KAAK;MACvB,IAAI,CAACP,MAAM,CAACoD,KAAK,CAACD,GAAG,CAAC;IAC7B,CAAC,CAAC;EACN;EAEAP,cAAcA,CAAA;IACV,IAAI,CAAC9C,aAAa,CAACwD,gBAAgB,EAAE,CAACN,SAAS,CAACC,QAAQ,IAAG;MACzD,IAAI,CAACtC,aAAa,GAAGsC,QAAQ;MAC9B,IAAI,CAAC1B,QAAQ,GAAE,IAAI,CAACxB,mBAAmB,CAACwD,gBAAgB,CAAC,IAAI,CAAC5C,aAAa,EAAC,MAAM,CAAC;IACpF,CAAC,EAAEyC,KAAK,IAAI;MACP,IAAI,CAACpD,MAAM,CAACoD,KAAK,CAACA,KAAK,CAAC;IAC7B,CAAC,CAAC;EACN;EAEAN,WAAWA,CAAA;IACT,IAAI,CAAChD,aAAa,CAAC0D,eAAe,EAAE,CAACR,SAAS,CAACpC,YAAY,IAAG,IAAI,CAACA,YAAY,GAAGA,YAAY,EAACuC,GAAG,IAAE;MAAE,IAAI,CAACnD,MAAM,CAACoD,KAAK,CAACD,GAAG,CAAC;IAAC,CAAC,CAAC;EACjI;EAEAT,aAAaA,CAAA;IACT,IAAI,CAAC5C,aAAa,CAAC2D,sBAAsB,EAAE,CAACT,SAAS,CAACU,QAAQ,IAAG;MAC/D,IAAI,CAAC3C,UAAU,GAAG2C,QAAQ;MAC1B,IAAI,IAAI,CAAC3B,WAAW,CAAC4B,mBAAmB,CAAC,CAAC,CAAC,CAACC,IAAI,IAAI,6BAA6B,IAC/E,IAAI,CAAC7B,WAAW,CAAC4B,mBAAmB,CAAC,CAAC,CAAC,CAACC,IAAI,IAAI,8BAA8B,IAC9E,IAAI,CAAC7B,WAAW,CAAC4B,mBAAmB,CAAC,CAAC,CAAC,CAACC,IAAI,IAAI,6BAA6B,IAC7E,IAAI,CAAC7B,WAAW,CAAC4B,mBAAmB,CAAC,CAAC,CAAC,CAACC,IAAI,IAAI,aAAa,IAC7D,IAAI,CAAC7B,WAAW,CAAC4B,mBAAmB,CAAC,CAAC,CAAC,CAACC,IAAI,IAAI,4BAA4B,EAAE;QAC9E,IAAI,CAAC/C,UAAU,GAAG,IAAI;QACtB,IAAI,CAACI,UAAU,GAAG,QAAQ;QAC1B,IAAI4C,GAAG,GAAG,IAAI,CAAC9B,WAAW,CAAC+B,eAAe,GAAG,GAAG,GAAG,IAAI,CAAC/B,WAAW,CAACgC,cAAc,GAAG,GAAG,GAAG,IAAI,CAAChC,WAAW,CAACiC,UAAU;QACtH;QACA,IAAI,CAAC7D,cAAc,CAAC3B,MAAM,GAAG,IAAI,CAACuD,WAAW,CAACiC,UAAU;QACxD,IAAI,CAAC9C,UAAU,GAAG,IAAI,CAACH,UAAU,CAACkD,IAAI,CAACC,CAAC,IAAEA,CAAC,CAACF,UAAU,IAAI,IAAI,CAACjC,WAAW,CAACiC,UAAU,CAAC,CAACG,UAAU;QACjG,IAAI,CAACC,YAAY,CAAC,IAAI,CAACrC,WAAW,CAACsC,QAAQ,CAAC;OAC7C,MAAM;QACL,IAAI,CAACxD,UAAU,GAAG,KAAK;;IAE3B,CAAC,CAAC;EACN;EAGAuD,YAAYA,CAACE,IAAI;IACb,IAAI,CAACxE,aAAa,CAACyE,kBAAkB,CAACD,IAAI,CAAC,CAACtB,SAAS,CAACU,QAAQ,IAAG;MAC9D,IAAI,CAAC5C,SAAS,GAAG4C,QAAQ;IAC7B,CAAC,CAAC;EACL;EAEAb,yBAAyBA,CAAA;IACrB,IAAI,CAACzB,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACD,eAAe,GAAG,EAAE;IACzB,IAAI,CAACrB,aAAa,CAAC0E,uBAAuB,EAAE,CAACxB,SAAS,CAACyB,IAAI,IAAG;MAC5D,IAAIA,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAACtD,uBAAuB,GAAGqD,IAAI;;IAEvC,CAAC,EAAEtB,GAAG,IAAG;MACP,IAAI,CAACnD,MAAM,CAACoD,KAAK,CAACD,GAAG,CAAC;IACxB,CAAC,CAAC;EACL;EAEAwB,iBAAiBA,CAACC,GAAG;IACnBC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IACf,IAAI,CAACzD,eAAe,GAAG,EAAE;IACzB,KAAI,IAAI4D,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACH,GAAG,CAACF,MAAM,EAACK,CAAC,EAAE,EAAC;MAC3B,IAAIC,SAAS,GAAG,IAAI,CAAC5D,uBAAuB,CAAC6C,IAAI,CAACgB,CAAC,IAAIA,CAAC,CAAC7G,IAAI,IAAIwG,GAAG,CAACG,CAAC,CAAC,CAAC;MACxE,IAAIT,IAAI,GAAG;QACV,oBAAoB,EAAEU,SAAS,CAAC,IAAI;OACpC;MACF,IAAI,CAAClF,aAAa,CAACoF,cAAc,CAACZ,IAAI,CAAC,CAACtB,SAAS,CAACyB,IAAI,IAAG;QACvD,IAAI,CAACtD,eAAe,CAACgE,IAAI,CAACV,IAAI,CAAC;QAC/B,IAAI,CAACtD,eAAe,GAAG,IAAI,CAACA,eAAe,CAACiE,IAAI,EAAE;MACpD,CAAC,CAAC;;EAEN;EAIDC,WAAWA,CAAA;IACT,IAAI,CAAChE,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACnB,cAAc,CAAClC,OAAO,GAAG,EAAE;IAChC,IAAI,CAACkC,cAAc,CAACjC,UAAU,GAAG,EAAE;IACnC,IAAG,IAAI,CAACiC,cAAc,CAACnC,YAAY,IAAE,KAAK,IAAI,IAAI,CAACmC,cAAc,CAACnC,YAAY,IAAE,EAAE,EAAC;MACjF,IAAI,CAACoC,MAAM,CAACnC,OAAO,GAAG,IAAI;MAC3B,IAAI,CAAC6B,aAAa,CAACwF,kBAAkB,CAAC,IAAI,CAACnF,cAAc,CAACnC,YAAY,CAAC,CAACgF,SAAS,CAACC,QAAQ,IAAE;QACxF,IAAI,CAAC5B,WAAW,GAAG4B,QAAQ;QAC3B,IAAI,CAAC5B,WAAW,CAAC6B,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE;UAAC,IAAI,EAAC,MAAM;UAAC,MAAM,EAAC;QAAK,CAAC,CAAC;QAC1D,IAAI,CAAC9C,MAAM,CAACnC,OAAO,GAAG,KAAK;MAC7B,CAAC,EACAkF,GAAG,IAAE;QACF,IAAI,CAACnD,MAAM,CAACoD,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAAC/C,MAAM,CAACnC,OAAO,GAAG,KAAK;MAChC,CAAC,CAAC;KACF,MAAM,IAAG,IAAI,CAACkC,cAAc,CAACnC,YAAY,IAAE,KAAK,EAAC;MAChD,IAAI,CAACqD,WAAW,CAAC8D,IAAI,CAAC;QAAC,IAAI,EAAC,MAAM;QAAC,MAAM,EAAC;MAAK,CAAC,CAAC;;EAEtD;EAEAI,cAAcA,CAAA;IACX,IAAI,CAACjE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACnB,cAAc,CAACjC,UAAU,GAAG,EAAE;IACnC,IAAG,IAAI,CAACiC,cAAc,CAAClC,OAAO,IAAE,MAAM,EAAC;MACpC,IAAI,CAACmC,MAAM,CAACE,UAAU,GAAG,IAAI;MAC7B,IAAI,CAACR,aAAa,CAAC0F,0BAA0B,CAAC,IAAI,CAACrF,cAAc,CAAClC,OAAO,CAAC,CAAC+E,SAAS,CAACC,QAAQ,IAAE;QAC5F,IAAI,CAAC3B,cAAc,GAAG2B,QAAQ;QAC9B,IAAI,CAAC3B,cAAc,CAAC4B,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE;UAAC,IAAI,EAAC,MAAM;UAAC,MAAM,EAAC;QAAK,CAAC,CAAC;QAC5D,IAAI,CAAC9C,MAAM,CAACE,UAAU,GAAG,KAAK;MAClC,CAAC,EAAC6C,GAAG,IAAE;QACF,IAAI,CAACnD,MAAM,CAACoD,KAAK,CAACD,GAAG,CAAC;QACrB,IAAI,CAAC/C,MAAM,CAACE,UAAU,GAAG,KAAK;MACpC,CAAC,CAAC;KACJ,MAAK,IAAG,IAAI,CAACH,cAAc,CAAClC,OAAO,IAAE,MAAM,EAAC;MAC3C,IAAI,CAACqD,cAAc,CAAC6D,IAAI,CAAC;QAAC,IAAI,EAAC,MAAM;QAAC,MAAM,EAAC;MAAK,CAAC,CAAC;;EAEzD;EAEAM,YAAYA,CAAA;IACT,IAAInB,IAAI,GAAC;MACP,SAAS,EAAC,IAAI,CAACnE,cAAc,CAAC/B;KAC/B;IACD,IAAI,CAAC+B,cAAc,CAAC9B,MAAM,GAAG,EAAE;IAC/B,IAAI,CAAC8B,cAAc,CAAC7B,KAAK,GAAG,EAAE;IAC9B,IAAI,CAAC6B,cAAc,CAAC5B,IAAI,GAAG,EAAE;IAC7B,IAAI,CAAC4B,cAAc,CAACzB,MAAM,GAAG,EAAE;IAC/B,IAAI,CAACoB,aAAa,CAAC4F,YAAY,CAACpB,IAAI,CAAC,CAACtB,SAAS,CAAEC,QAAa,IAAI;MAChE,IAAI,CAACxB,UAAU,GAAC,EAAE;MAClB,IAAI,CAACA,UAAU,GAACwB,QAAQ;IAC1B,CAAC,EAACG,KAAK,IAAE;MACN,IAAI,CAACpD,MAAM,CAACoD,KAAK,CAACA,KAAK,CAAC;IAC3B,CAAC,CAAC;EACN;EAECuC,WAAWA,CAAA;IACV,IAAIrB,IAAI,GAAC;MACP,SAAS,EAAE,IAAI,CAACnE,cAAc,CAAC/B,IAAI;MACnC,QAAQ,EAAC,IAAI,CAAC+B,cAAc,CAAC9B;KAC9B;IACD,IAAI,CAAC8B,cAAc,CAAC7B,KAAK,GAAG,EAAE;IAC9B,IAAI,CAAC6B,cAAc,CAAC5B,IAAI,GAAG,EAAE;IAC7B,IAAI,CAAC4B,cAAc,CAACzB,MAAM,GAAG,EAAE;IAC/B,IAAI,CAACoB,aAAa,CAAC8F,WAAW,CAACtB,IAAI,CAAC,CAACtB,SAAS,CAAEC,QAAa,IAAI;MAC/D,IAAI,CAACzB,SAAS,GAAC,EAAE;MACjB,IAAI,CAACA,SAAS,GAACyB,QAAQ;IAExB,CAAC,EAACG,KAAK,IAAE;MACP,IAAI,CAACpD,MAAM,CAACoD,KAAK,CAACA,KAAK,CAAC;IAC1B,CAAC,CAAC;EACL;EAEAyC,WAAWA,CAAA;IACT,IAAIvB,IAAI,GAAC;MACP,SAAS,EAAE,IAAI,CAACnE,cAAc,CAAC/B,IAAI;MACnC,QAAQ,EAAE,IAAI,CAAC+B,cAAc,CAAC9B,MAAM;MACpC,OAAO,EAAC,IAAI,CAAC8B,cAAc,CAAC7B;KAC7B;IACD,IAAI,CAAC6B,cAAc,CAAC5B,IAAI,GAAG,EAAE;IAC7B,IAAI,CAAC4B,cAAc,CAACzB,MAAM,GAAG,EAAE;IAC/B,IAAI,CAACoB,aAAa,CAACgG,UAAU,CAACxB,IAAI,CAAC,CAACtB,SAAS,CAAEC,QAAa,IAAI;MAC9D,IAAI,CAACvB,QAAQ,GAAC,EAAE;MAChB,IAAI,CAACA,QAAQ,GAACuB,QAAQ;IACxB,CAAC,EAACG,KAAK,IAAE;MACN,IAAI,CAACpD,MAAM,CAACoD,KAAK,CAACA,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEA2C,YAAYA,CAAA;IACX,IAAIzB,IAAI,GAAC;MACP,SAAS,EAAE,IAAI,CAACnE,cAAc,CAAC/B,IAAI;MACnC,QAAQ,EAAE,IAAI,CAAC+B,cAAc,CAAC9B,MAAM;MACpC,OAAO,EAAE,IAAI,CAAC8B,cAAc,CAAC7B,KAAK;MACjC,MAAM,EAAC,IAAI,CAAC6B,cAAc,CAAC5B;KAC7B;IACA,IAAI,CAAC4B,cAAc,CAACzB,MAAM,GAAG,EAAE;IAC/B,IAAI,CAACoB,aAAa,CAACkG,YAAY,CAAC1B,IAAI,CAAC,CAACtB,SAAS,CAAEC,QAAa,IAAI;MAClE,IAAI,CAACtB,UAAU,GAAC,EAAE;MAClB,IAAI,CAACA,UAAU,GAACsB,QAAQ;IACxB,CAAC,EAACG,KAAK,IAAE;MACR,IAAI,CAACpD,MAAM,CAACoD,KAAK,CAACA,KAAK,CAAC;IACzB,CAAC,CAAC;EACJ;EAEA6C,cAAcA,CAAA;IACR,IAAG,IAAI,CAAChF,UAAU,IAAE,MAAM,EAAC;MACzB,IAAI,CAACd,cAAc,CAAC3B,MAAM,GAAG,EAAE;MAC/B,IAAI,CAAC2B,cAAc,CAAC1B,SAAS,GAAG,EAAE;MAClC,IAAI,CAAC0B,cAAc,CAACzB,MAAM,GAAG,EAAE;MAC/B,IAAI,CAACkD,UAAU,GAAG,EAAE;MACpB,IAAI,CAACZ,UAAU,GAAG,EAAE;MACpB,IAAI,CAACa,KAAK,GAAG,EAAE;MACf,IAAI,CAAChD,SAAS,GAAG,EAAE;MACnB,IAAI,CAACsB,cAAc,CAAChB,oCAAoC,GAAG,EAAE;MAC7D,IAAI,CAACgB,cAAc,CAACjB,6BAA6B,GAAG,EAAE;KACvD,MAAM;MACL,IAAI,CAACiB,cAAc,CAAC1B,SAAS,GAAG,EAAE;MAClC,IAAI,CAAC0B,cAAc,CAACzB,MAAM,GAAG,EAAE;MAC/B,IAAI,CAACyB,cAAc,CAAC3B,MAAM,GAAG,EAAE;MAC/B,IAAI,CAACoD,UAAU,GAAG,EAAE;MACpB,IAAI,CAACZ,UAAU,GAAG,EAAE;MACpB,IAAI,CAACa,KAAK,GAAG,EAAE;MACf,IAAI,CAAChD,SAAS,GAAG,EAAE;MACnB,IAAI,CAACsB,cAAc,CAAChB,oCAAoC,GAAG,EAAE;MAC7D,IAAI,CAACgB,cAAc,CAACjB,6BAA6B,GAAG,EAAE;;EAE5D;EACAgH,cAAcA,CAAA;IACZ,IAAI,CAAC/F,cAAc,CAAC1B,SAAS,GAAG,EAAE;EACpC;EACA0H,cAAcA,CAAA;IACZ,IAAI,CAAChG,cAAc,CAACtB,SAAS,GAAG,EAAE;EACpC;EAEAuH,cAAcA,CAACC,KAAK;IAChB,IAAI,CAACrF,UAAU,GAAIqF,KAAK,CAACC,IAAI,CAACC,SAAS,GAAC,GAAG,GAACF,KAAK,CAACC,IAAI,CAACtC,UAAU;IACjE,IAAI,CAAC7D,cAAc,CAAC3B,MAAM,GAAG6H,KAAK,CAACC,IAAI,CAACtC,UAAU;IAClD,IAAI,CAAC7D,cAAc,CAACzB,MAAM,GAAG,EAAE;IAC/B,IAAI,CAACwC,UAAU,GAAImF,KAAK,CAACC,IAAI,CAACnC,UAAU;IACxC,IAAI,CAACtF,SAAS,GAAG,EAAE;IACnB,IAAI,CAACsB,cAAc,CAACtB,SAAS,GAAG,EAAE;IAClC,IAAI,CAACsB,cAAc,CAACjB,6BAA6B,GAAGmH,KAAK,CAACC,IAAI,CAACE,EAAE;IACjE,IAAI,CAACpC,YAAY,CAACiC,KAAK,CAACC,IAAI,CAACE,EAAE,CAAC;EACpC;EAEAC,aAAaA,CAACJ,KAAK;IACjB,IAAI,CAACxH,SAAS,GAAIwH,KAAK,CAACC,IAAI,CAAClI,IAAI,GAAC,GAAG,GAACiI,KAAK,CAACC,IAAI,CAACI,IAAI;IACrD,IAAI,CAACvG,cAAc,CAACtB,SAAS,GAAGwH,KAAK,CAACC,IAAI,CAACI,IAAI;IAC/C,IAAI,CAACvG,cAAc,CAAChB,oCAAoC,GAAGkH,KAAK,CAACC,IAAI,CAACE,EAAE;EAC1E;EAEAG,cAAcA,CAACN,KAAK;IACjB,IAAI,CAACzE,UAAU,GAAIyE,KAAK,CAACC,IAAI,CAAClI,IAAI,GAAE,GAAG,GAAGiI,KAAK,CAACC,IAAI,CAACI,IAAI;IACzD,IAAI,CAACvG,cAAc,CAACzB,MAAM,GAAG2H,KAAK,CAACC,IAAI,CAAClI,IAAI;IAC5C,IAAI,CAAC+B,cAAc,CAAC3B,MAAM,GAAG,EAAE;EAClC;EAEAoI,aAAaA,CAACP,KAAK;IACjB,IAAI,CAACxE,KAAK,GAAIwE,KAAK,CAACC,IAAI,CAACC,SAAS,GAAC,GAAG,GAACF,KAAK,CAACC,IAAI,CAACtC,UAAU;IAC5D,IAAI,CAAC7D,cAAc,CAAC1B,SAAS,GAAG4H,KAAK,CAACC,IAAI,CAACtC,UAAU,EAAC;IACtD,IAAI,CAAC7D,cAAc,CAAChB,oCAAoC,GAAGkH,KAAK,CAACC,IAAI,CAACE,EAAE;EAC1E;EASCK,eAAeA,CAACR,KAAc;IAC3B,IAAI,CAACS,cAAc,GAAGT,KAAK;EAC/B;EAECU,eAAeA,CAACV,KAAc;IAC3B,IAAI,CAACW,cAAc,GAAGX,KAAK;EAC/B;EAEEY,cAAcA,CAACZ,KAAc;IAC3B,IAAI,CAACa,aAAa,GAAGb,KAAK;EAC9B;EAECc,kBAAkBA,CAACd,KAAc;IAChC,IAAGA,KAAK,EAAC;MACP,IAAI,CAACrG,MAAM,CAACoH,IAAI,CAAC,0CAA0C,CAAC;;EAEhE;EAEAC,kBAAkBA,CAAChB,KAAc;IAC/B,IAAI,CAACiB,qBAAqB,GAAGjB,KAAK;EACpC;EAEEkB,mBAAmBA,CAAClB,KAAc;IAClC,IAAI,CAACmB,sBAAsB,GAAGnB,KAAK;EACrC;EAECoB,mBAAmBA,CAACpB,KAAc;IACjC,IAAI,CAACqB,sBAAsB,GAAGrB,KAAK;EACrC;EAGAsB,iBAAiBA,CAAA;IACd,IAAI,CAAC7F,YAAY,GAAGxC,UAAU,CAACsI,MAAM,CAAEC,QAAa,IAAI;MACjDA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACjG,KAAK,CAAC;IAC7B,CAAC,CAAC,CACDkG,IAAI,CACHxI,QAAQ,CAAEyI,KAAa,IAAK,IAAI,CAACC,mBAAmB,CAACD,KAAK,CAAC,CAAC,CAC7D;EACP;EAEAC,mBAAmBA,CAACD,KAAa;IAC/B,OAAO,IAAI,CAAClI,aAAa,CAACoI,aAAa,CAACF,KAAK,CAAC,CAC7CD,IAAI,CAACvI,GAAG,CAAE2I,OAAc,IAAKA,OAAO,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC9B,SAAS,CAAC+B,WAAW,EAAE,CAACC,OAAO,CAACP,KAAK,CAACM,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACtH;EAEAE,SAASA,CAAA;IACP,IAAI,CAACpI,MAAM,CAACC,WAAW,GAAG,IAAI;IAC9B,IAAIoI,GAAG,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACxI,cAAc,CAAC;IAChDsI,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,CAACtI,cAAc,CAACnC,YAAY,GAAG,IAAI,CAAC+B,mBAAmB,CAAC6I,eAAe,CAAC,IAAI,CAACnI,gBAAgB,EAAC,IAAI,EAAC,IAAI,CAACN,cAAc,CAACnC,YAAY,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI;IAC5KyK,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAACtI,cAAc,CAAClC,OAAO,GAAI,IAAI,CAAC8B,mBAAmB,CAAC6I,eAAe,CAAC,IAAI,CAACvH,WAAW,EAAC,IAAI,EAAC,IAAI,CAAClB,cAAc,CAAClC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI;IAE1J;IACA,IAAI,OAAO,IAAI,CAACkC,cAAc,CAACrB,aAAc,IAAI,QAAQ,IAAK,IAAI,CAACqB,cAAc,CAACrB,aAAa,IAAI,IAAI,EAAC;MACtG2J,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC1I,mBAAmB,CAAC8I,WAAW,CAAC,IAAI,CAAC1I,cAAc,CAACrB,aAAa,CAAC;;IAEhG,IAAI,OAAO,IAAI,CAACqB,cAAc,CAACpB,WAAY,IAAI,QAAQ,IAAK,IAAI,CAACoB,cAAc,CAACpB,WAAW,IAAI,IAAI,EAAC;MAClG0J,GAAG,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC1I,mBAAmB,CAAC8I,WAAW,CAAC,IAAI,CAAC1I,cAAc,CAACpB,WAAW,CAAC;;IAG5F,IAAI,IAAI,CAACkC,UAAU,IAAI,MAAM,EAAE;MAC9BwH,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI;KAC3B,MAAM;MACNA,GAAG,CAAC,eAAe,CAAC,GAAI,KAAK;;IAE9B,IAAI,CAACtI,cAAc,CAAC,QAAQ,CAAC,GAAG,IAAI,CAACA,cAAc,CAACzB,MAAM,GAAG,IAAI,CAACyB,cAAc,CAACzB,MAAM,GAAG,IAAI;IAE/F,IAAI,CAACoB,aAAa,CAACgJ,oBAAoB,CAACL,GAAG,CAAC,CAACzF,SAAS,CAACC,QAAQ,IAAE;MAC/D,IAAI,CAAC7C,MAAM,CAACC,WAAW,GAAG,KAAK;MAC/B,IAAI,CAAC0I,YAAY,CAAC9F,QAAQ,CAAC;IAC7B,CAAC,EAACE,GAAG,IAAE;MACL,IAAI,CAACnD,MAAM,CAACoD,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAAC/C,MAAM,CAACC,WAAW,GAAG,KAAK;IACjC,CAAC,CAAC;EACJ;EAEA0I,YAAYA,CAACC,QAAQ;IACnB,IAAI,CAAClJ,aAAa,CAACiJ,YAAY,CAACC,QAAQ,CAAC,CAAChG,SAAS,CAACC,QAAQ,IAAE;MACxD,IAAI,CAAC7C,MAAM,CAACI,UAAU,GAAG,KAAK;MAC9B,IAAIyI,SAAS,GAAG,KAAK;MACrB,IAAIC,SAAS,GAAG,iBAAiB;MACjC,IAAIC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACnCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;MACxCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,CAAC,CAAC;MAC5B,IAAIM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACzG,QAAQ,CAAC,EAAE;QAAE0G,IAAI,EAAET;MAAS,CAAE,CAAC;MACpD,IAAIU,GAAG,GAAE1H,MAAM,CAAC2H,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACzCN,CAAC,CAACY,IAAI,GAAGH,GAAG;MACZT,CAAC,CAACa,QAAQ,GAAG,wBAAwB;MACrCb,CAAC,CAACc,KAAK,EAAE;IACf,CAAC,EAAC9G,GAAG,IAAE;MACH,IAAI,CAACnD,MAAM,CAACoD,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAAC/C,MAAM,CAACI,UAAU,GAAG,KAAK;IAClC,CAAC,CAAC;EACJ;EAAC,QAAA0J,CAAA,G;;;;;;;;;;;;AAvZUrK,sBAAsB,GAAAsK,UAAA,EALlC/K,SAAS,CAAC;EACTgL,QAAQ,EAAE,oBAAoB;EAC9BC,QAAA,EAAAC,oBAA8C;;CAE/C,CAAC,C,EACWzK,sBAAsB,CA2ZlC;SA3ZYA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}