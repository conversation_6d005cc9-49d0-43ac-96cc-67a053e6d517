import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter,
  AfterViewInit,
} from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { forkJoin, from, Observable, of, catchError, tap } from "rxjs";
import { mergeMap, map, switchMap } from "rxjs/operators";
import { ToastrService } from "ngx-toastr";
import { UserService } from "../user.service";
import { environment } from "../../../environments/environment";

import { JwtService } from "../jwt.service";
import { MsalService } from "@azure/msal-angular";
import { DatePipe } from "@angular/common";
import { AES256GCM } from "src/app/shared/services/aes-256-gcm";

@Component({
  selector: "auth-page",
  templateUrl: "./auth.component.html",
  encapsulation: ViewEncapsulation.None,
})
export class AuthComponent implements OnInit, AfterViewInit {
  @ViewChild("recaptcha") recaptchaElement: ElementRef;
  public isSubmitting: boolean = false;
  public authForm: FormGroup;
  public referenceId: any;
  public getKey: any;
  public captchaData = "";
  public currencyDetails: any;
  public browserLat: any;
  public browserLng: any;
  public imgArray: any[] = [];
  public logo: any;
  public selectedTenantId: string = "";
  public companiesList: any[] = [];
  public serverBusy: boolean = false;
  public helpDeskEmail = "";
  public helpDeskNo = "";
  public gCaptchaPvtKey: string = environment.gcaptcha;
  public gCaptchaEnabled: boolean = environment.gCaptchaEnabled;
  @Output() childEvent = new EventEmitter();
  public disableButton: boolean = false;
  public authType: String = "";
  captcha: any;
  capt: any;
  showPassword = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private fb: FormBuilder,
    private jwtService: JwtService,
    public toastr: ToastrService,
    private msalService: MsalService
  ) {
    this.buildAuthForm();
    this.userService.purgeAuth();
    this.imgArray = this.userService.imgArray();
    this.logo = this.userService.logo();
  }

  ngOnInit() {
    this.fetchGeoPosition();
    this.fetchQueryParams();
    this.captchaData = "";
  }

  ngAfterViewInit() {}

  private fetchGeoPosition() {
    navigator.geolocation.getCurrentPosition(function (location) {
      window.localStorage["browserLat"] = location.coords.latitude;
      window.localStorage["browserLng"] = location.coords.longitude;
    });
    this.browserLat = window.localStorage["browserLat"];
    this.browserLng = window.localStorage["browserLng"];
  }

  private fetchQueryParams() {
    this.authForm.controls["tenant"].setValue("");
    this.authForm.controls["tenantId"].setValue("");
   this.route.queryParams.subscribe((params) => {
      const tenantId =
        params["tenantid"] || params["TenantId"] || params["tenantId"] || "3" || localStorage.getItem('TenantId');
      window.localStorage["TenantId"] = tenantId || "";
      this.selectedTenantId = tenantId;
      this.imgArray = this.userService.imgArray(tenantId);
      this.logo = this.userService.logo(tenantId);
      this.setCompanyId(this.selectedTenantId);
      this.refreshCaptcha();
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }
  geyAPIKey() {
    this.referenceId = "";
    this.getKey = "";
    this.serverBusy = true;
    this.userService.getKey().subscribe(
      (data) => {
        setTimeout(() => {
          this.referenceId = data.referenceId;
          this.getKey = data.key;
          this.serverBusy = false;
        }, 3000);
      },
      (err) => {
        this.serverBusy = false;
        this.toastr.error(
          "Company not available, please contact administrator"
        );
      }
    );
  }

  /**
   * Build Auth Form
   */
  buildAuthForm() {
    this.authForm = this.fb.group(
      {
        email: ["", Validators.required],
        password: ["", Validators.required],
        tenant: [""],
        tenantId: ["", Validators.required],
        userType: ["password", Validators.required],
        rememberMe: [""],
      },
      { updateOn: "blur" }
    );
  }

  /**
   * Get Companies List
   */
  getCompaniesList($event) {
    this.companiesList = Observable.create((observer: any) => {
      observer.next($event.target.value);
    }).pipe(mergeMap((token: string) => this.searchCompanyList(token)));
  }

  /**
   * If No Results in Typeahead
   */
  typeaheadNoResults(event: boolean): void {
    if (event) {
      this.toastr.info("Please enter correct company name");
    }
  }

  /**
   * Search Company List
   */
  searchCompanyList(data: string): Observable<any> {
    const token = {
      type: "ENC",
      name: data,
    };
    return this.userService
      .getTenats(token)
      .pipe(
        map((results: any[]) =>
          results.filter(
            (res) => res.value.toLowerCase().indexOf(data.toLowerCase()) > -1
          )
        )
      );
  }

  /**
   * Set Company ID
   */
  setCompanyId(event) {
    if (window.localStorage["TenantId"] && !event["item"]) {
      this.selectedTenantId = window.localStorage["TenantId"];
      this.authForm.controls["tenant"].setValue(this.logo.clientName);
      this.authForm.controls["tenantId"].setValue(this.logo.clientName);
      this.imgArray = this.userService.imgArray(this.selectedTenantId);
      this.logo = this.userService.logo(this.selectedTenantId);
      this.geyAPIKey();
      this.childEvent.emit(event);
    } else {
      if (event) {
        this.authForm.controls["tenant"].setValue("");
        window.localStorage["TenantId"] = "";
        this.selectedTenantId = event["item"].id;
        window.localStorage["TenantId"] = event["item"].id;
        this.authForm.controls["tenant"].setValue(event["item"]?.value);
        this.authForm.controls["tenantId"].setValue(event["item"]?.value);
        this.imgArray = this.userService.imgArray(this.selectedTenantId);
        this.logo = this.userService.logo(this.selectedTenantId);
        this.geyAPIKey();
        this.childEvent.emit(event);
      }
    }
  }

  /**
   * Submit Form
   */
  submitForm() {
    // if (!this.capt) {
    //   this.toastr.warning("Please enter captcha");
    //   return false;
    // }

    // if (this.captcha !== this.capt) {
    //   this.toastr.warning("Please enter valid captcha");
    //   return false;
    // }

    const userCredentials = Object.assign({}, this.authForm.value);

    if (userCredentials?.tenant !== userCredentials?.tenantId) {
      this.toastr?.warning("Please select a valid company name.");
      return;
    }

    if (
      !userCredentials.email ||
      !userCredentials.password ||
      !userCredentials.tenantId
    ) {
      this.toastr.warning(
        "Please enter the company name, username, and password."
      );
      return false;
    }

    this.attemptAuth(userCredentials);
  }

  /**
   * Attempt Auth
   */
  attemptAuth(userCredentials: any) {
    this.isSubmitting = false;

    const AES = new AES256GCM(this.getKey);

    forkJoin({
      grant_type: from(AES.encrypt(userCredentials.userType)),
      UserName: from(AES.encrypt(userCredentials.email)),
      Password: from(AES.encrypt(userCredentials.password)),
      Client_id: from(AES.encrypt("ENTigerClient")),
      Scope: from(AES.encrypt("openid profile entigerapinew")),
      Client_secret: from(AES.encrypt("jgd123$")),
      referenceId: of(this.referenceId),
    })
      .pipe(switchMap((data: any) => this.userService.attemptAuth(data)))
      .subscribe({
        next: (data: any) => {
          if (data && data.message) {
            this.toastr.warning(data.message);
          }
          this.setLoginTime(data?.access_token);
        },
        error: (err: any) => {
          this.isSubmitting = false;
          this.handleAuthError(err);
        },
      });
  }

  private setLoginTime(token: string) {
    const inputJson = {
      logInLongitude: this.browserLng,
      logInLatitude: this.browserLat,
      logInTime: new DatePipe("en").transform(
        new Date(),
        "yyyy-MM-dd HH:mm:ss"
      ),
      sessionId: `Bearer ${token}`,
    };
    this.userService.setLoginTime(inputJson).subscribe(
      () => {
         setTimeout(() => {
        this.fetchCurrentUserDetails();
         }, 3000);
      },
      (err) => {
        this.toastr.error(err);
        this.userService.purgeAuth();
      }
    );
  }

  /**
   * Fetch Current User Details
   */
  fetchCurrentUserDetails() {
    this.userService.fetchCurrentUserDetail().subscribe(
      (response: any) => {
        if (!response.id) {
          this.toastr.error("Invalid username/password");
          this.isSubmitting = false;
        } else {
          setTimeout(() => {
          this.displayMessages(response);
           }, 3000);
          setTimeout(() => {
          this.fetchCurrency();
           }, 3000);
          setTimeout(() => {
          this.handleUserRole(response.responsibilityInfos[0].role);
           }, 3000);

          setTimeout(() => {
            this.toastr.success("Welcome to BOB ENCollect");
          }, 3000);
        }
      },
      (err) => {
        this.isSubmitting = false;
        this.toastr.error(err);
      }
    );
  }

  /**
   * Handle Loggedin User Role
   */
  private handleUserRole(userRole: string) {
    const roleRoutes = {
      // AgencyToBackEndExternalBIAP: "/encollect/dashboard/agency-dashboard",
      // AgencyToFrontEndExternalBIAP: "/encollect/dashboard/agency-dashboard",
      // AgencyToFrontEndExternalFOS: "/encollect/dashboard/agent-dashboard",
      // BankToFrontEndInternalBIBP: "/encollect/dashboard/staff-dashboard",
      // BankToFrontEndInternalFOS: "/encollect/dashboard/staff-dashboard",
      // BankToBackEndInternalBIBP: "/encollect/dashboard/staff-dashboard",
      // BankToBackEndInternalBIHP: "/encollect/dashboard/staff-dashboard",
      // BankToFrontEndInternalTC: "/encollect/dashboard/telecaller-dashboard",
      // AgencyToFrontEndExternalTC: "/encollect/dashboard/telecaller-dashboard",
    };

    const route = roleRoutes[userRole] || "/home";
    this.router.navigateByUrl(route);
  }

  /**
   * Fetch Currency
   */
  fetchCurrency() {
    this.userService.getCurrency().subscribe((data: any) => {
      // Handle currency data if needed
    });
  }

  private handleAuthError(err: any) {
    if (err?.status === 0) {
      this.toastr.error(
        "Please check your internet connectivity or contact the application administrator."
      );
    } else if (Array.isArray(err)) {
      err.forEach((error) => this.toastr.error(error));
    } else if (err?.message) {
      this.toastr.error(err.message);
    }
    this.geyAPIKey();
  }
  submitFormStaff() {
    // if (!this.capt) {
    //   this.toastr.warning("Please enter captcha");
    //   return false;
    // }

    // if (this.captcha !== this.capt) {
    //   this.toastr.warning("Please enter valid captcha");
    //   return false;
    // }

    // Ensure userCredentials is defined here
    const userCredentials = Object.assign({}, this.authForm.value);

    if (userCredentials?.tenant !== userCredentials?.tenantId) {
      this.toastr?.warning("Please select a valid company name.");
      return;
    }

    if (
      !userCredentials.email ||
      !userCredentials.password ||
      !userCredentials.tenantId
    ) {
      this.toastr.warning(
        "Please enter the company name, username, and password."
      );
      return false;
    }

    this.attemptAuthStaff(userCredentials);
  }

  attemptAuthStaff(userCredentials: any) {
    this.isSubmitting = false;

    const AES = new AES256GCM(this.getKey);

    forkJoin({
      grant_type: from(AES.encrypt(userCredentials.userType)),
      UserName: from(AES.encrypt(userCredentials.email)),
      Password: from(AES.encrypt(userCredentials.password)),
      Client_id: from(AES.encrypt("ENTigerClient")),
      Scope: from(AES.encrypt("openid profile entigerapinew")),
      Client_secret: from(AES.encrypt("jgd123$")),
      referenceId: of(this.referenceId),
    })
      .pipe(switchMap((data: any) => this.userService.attemptAuthStaff(data)))
      .subscribe({
        next: (data: any) => {
          if (data && data.message) {
            this.toastr.warning(data.message);
          }
          this.setLoginTime(data?.access_token);
        },
        error: (err: any) => {
          this.isSubmitting = false;
          this.handleAuthError(err);
        },
      });
  }

  navigateToDashboard(response: any) {
    let userRole = response.responsibilityInfos[0].role;
    switch (userRole) {
      case "AgencyToBackEndExternalBIAP":
      case "AgencyToFrontEndExternalBIAP":
        this.router.navigateByUrl("/encollect/dashboard/agency-dashboard");
        break;
      case "AgencyToFrontEndExternalFOS":
        this.router.navigateByUrl("/encollect/dashboard/agent-dashboard");
        break;
      case "BankToFrontEndInternalBIBP":
      case "BankToFrontEndInternalFOS":
      case "BankToBackEndInternalBIBP":
      case "BankToBackEndInternalBIHP":
        this.router.navigateByUrl("/encollect/dashboard/staff-dashboard");
        break;
      case "BankToFrontEndInternalTC":
      case "AgencyToFrontEndExternalTC":
        this.router.navigateByUrl("/encollect/dashboard/telecaller-dashboard");
        break;
      default:
        this.router.navigateByUrl("/home");
    }
  }

  refreshCaptcha() {
    this.captcha = this.captchaText();
  }
  captchaText() {
    var text = "";
    var possible =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    for (var i = 0; i < 5; i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    return text;
  }

  private displayMessages(res: any) {
    if (res?.authorizationCardDateExpiryMessage) {
      this.toastr.success(res.authorizationCardDateExpiryMessage);
    }

    let combinedMessage = "";

    if (res?.lastSuccessLoginMessage) {
      combinedMessage += res.lastSuccessLoginMessage;
    }

    if (res?.lastFailLoginMessage) {
      if (combinedMessage) {
        combinedMessage += " ";
      }
      combinedMessage += res.lastFailLoginMessage;
    }

    if (combinedMessage) {
      this.toastr.success(combinedMessage);
    }
  }
}
